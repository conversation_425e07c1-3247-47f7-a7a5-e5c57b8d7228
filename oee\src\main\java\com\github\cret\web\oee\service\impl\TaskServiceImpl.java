package com.github.cret.web.oee.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.TaskStatus;
import com.github.cret.web.oee.repository.TaskStatusRepository;
import com.github.cret.web.oee.service.TaskService;

/**
 * 任务管理服务实现类
 */
@Service
public class TaskServiceImpl implements TaskService {

	private static final Logger log = LoggerFactory.getLogger(TaskServiceImpl.class);

	private final TaskStatusRepository taskStatusRepository;

	public TaskServiceImpl(TaskStatusRepository taskStatusRepository) {
		this.taskStatusRepository = taskStatusRepository;
	}

	@Override
	public TaskStatus createTask(String userId, String taskType, String taskParams) {
		String taskId = generateTaskId();
		TaskStatus taskStatus = new TaskStatus(taskId, userId, taskType);
		taskStatus.setTaskParams(taskParams);

		TaskStatus savedTask = taskStatusRepository.save(taskStatus);
		log.info("Created new task: taskId={}, userId={}, taskType={}", taskId, userId, taskType);

		return savedTask;
	}

	@Override
	public Optional<TaskStatus> getTaskStatus(String taskId) {
		return taskStatusRepository.findByTaskId(taskId);
	}

	@Override
	public Optional<TaskStatus> getTaskStatus(String taskId, String userId) {
		return taskStatusRepository.findByTaskIdAndUserId(taskId, userId);
	}

	@Override
	public TaskStatus updateTaskStatus(TaskStatus taskStatus) {
		return taskStatusRepository.save(taskStatus);
	}

	@Override
	public void markTaskAsProcessing(String taskId) {
		Optional<TaskStatus> taskOpt = getTaskStatus(taskId);
		if (taskOpt.isPresent()) {
			TaskStatus task = taskOpt.get();
			task.markAsProcessing();
			taskStatusRepository.save(task);
			log.info("Marked task as processing: taskId={}", taskId);
		}
		else {
			log.warn("Task not found when marking as processing: taskId={}", taskId);
		}
	}

	@Override
	public void updateTaskProgress(String taskId, int progress) {
		Optional<TaskStatus> taskOpt = getTaskStatus(taskId);
		if (taskOpt.isPresent()) {
			TaskStatus task = taskOpt.get();
			task.updateProgress(progress);
			taskStatusRepository.save(task);
			log.debug("Updated task progress: taskId={}, progress={}", taskId, progress);
		}
		else {
			log.warn("Task not found when updating progress: taskId={}", taskId);
		}
	}

	@Override
	public void markTaskAsCompleted(String taskId, String filePath, String fileName) {
		Optional<TaskStatus> taskOpt = getTaskStatus(taskId);
		if (taskOpt.isPresent()) {
			TaskStatus task = taskOpt.get();
			task.markAsCompleted(filePath, fileName);
			taskStatusRepository.save(task);
			log.info("Marked task as completed: taskId={}, fileName={}", taskId, fileName);
		}
		else {
			log.warn("Task not found when marking as completed: taskId={}", taskId);
		}
	}

	@Override
	public void markTaskAsFailed(String taskId, String errorMessage) {
		Optional<TaskStatus> taskOpt = getTaskStatus(taskId);
		if (taskOpt.isPresent()) {
			TaskStatus task = taskOpt.get();
			task.markAsFailed(errorMessage);
			taskStatusRepository.save(task);
			log.error("Marked task as failed: taskId={}, error={}", taskId, errorMessage);
		}
		else {
			log.warn("Task not found when marking as failed: taskId={}", taskId);
		}
	}

	@Override
	public List<TaskStatus> getUserTasks(String userId) {
		return taskStatusRepository.findByUserIdOrderByCreatedAtDesc(userId);
	}

	@Override
	public int cleanupExpiredTasks(int daysToKeep) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_MONTH, -daysToKeep);
		Date cutoffDate = calendar.getTime();

		List<TaskStatus> expiredTasks = taskStatusRepository
			.findByStatusAndCreatedAtBefore(TaskStatus.TaskStatusEnum.COMPLETED, cutoffDate);

		int cleanedCount = expiredTasks.size();
		if (cleanedCount > 0) {
			taskStatusRepository.deleteByCreatedAtBefore(cutoffDate);
			log.info("Cleaned up {} expired tasks older than {} days", cleanedCount, daysToKeep);
		}

		return cleanedCount;
	}

	@Override
	public String generateTaskId() {
		return UUID.randomUUID().toString();
	}

}
