# OEE异步导出API使用说明

## 概述

本文档介绍了OEE系统中新增的异步导出功能，该功能解决了大数据量导出时的性能问题，提供了更好的用户体验。

## 核心特性

- **异步处理**: 导出任务在后台异步执行，不阻塞前端界面
- **状态追踪**: 实时查询任务执行状态和进度
- **文件管理**: 自动管理导出文件的存储和清理
- **错误处理**: 完善的错误处理和重试机制

## API接口

### 1. 创建月度导出任务

**接口**: `POST /analyze/export-task/monthly`

**请求体**:
```json
{
  "workshopId": "WORKSHOP_001",
  "startTime": 1704067200000,
  "endTime": 1706745599000,
  "exportType": "MONTHLY"
}
```

**响应**:
```json
{
  "taskId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "message": "导出任务已创建，请使用任务ID查询进度"
}
```

**HTTP状态码**: 202 Accepted

### 2. 创建日度导出任务

**接口**: `POST /analyze/export-task/daily`

**请求体**:
```json
{
  "workshopId": "WORKSHOP_001",
  "startTime": 1704067200000,
  "endTime": 1704153599000,
  "exportType": "DAILY"
}
```

**响应**: 同月度导出任务

### 3. 查询任务状态

**接口**: `GET /analyze/export-task/{taskId}/status`

**响应示例**:

处理中:
```json
{
  "taskId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "PROCESSING",
  "progress": 50
}
```

已完成:
```json
{
  "taskId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "COMPLETED",
  "progress": 100,
  "fileName": "WORKSHOP_001车间202401月度OEE数据_1704067200000.xlsx"
}
```

失败:
```json
{
  "taskId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "FAILED",
  "progress": 0,
  "errorMessage": "数据库连接超时"
}
```

### 4. 下载导出文件

**接口**: `GET /analyze/export-task/{taskId}/download`

**响应**: Excel文件流

**注意**: 只有任务状态为`COMPLETED`时才能下载文件

## 任务状态说明

| 状态 | 说明 |
|------|------|
| PENDING | 任务已创建，等待处理 |
| PROCESSING | 任务正在处理中 |
| COMPLETED | 任务已完成，可以下载文件 |
| FAILED | 任务执行失败 |

## 使用流程

1. **创建任务**: 调用创建导出任务API，获得任务ID
2. **轮询状态**: 使用任务ID定期查询任务状态
3. **下载文件**: 当状态为COMPLETED时，调用下载API获取文件

## 前端集成示例

```javascript
// 1. 创建导出任务
async function createExportTask(workshopId, startTime, endTime, exportType) {
  const response = await fetch('/analyze/export-task/' + exportType.toLowerCase(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      workshopId,
      startTime,
      endTime,
      exportType
    })
  });
  
  const result = await response.json();
  return result.taskId;
}

// 2. 轮询任务状态
async function pollTaskStatus(taskId) {
  const response = await fetch(`/analyze/export-task/${taskId}/status`);
  const status = await response.json();
  
  if (status.status === 'COMPLETED') {
    // 任务完成，可以下载
    downloadFile(taskId);
  } else if (status.status === 'FAILED') {
    // 任务失败
    console.error('导出失败:', status.errorMessage);
  } else {
    // 继续轮询
    setTimeout(() => pollTaskStatus(taskId), 2000);
  }
  
  // 更新进度条
  updateProgress(status.progress);
}

// 3. 下载文件
function downloadFile(taskId) {
  window.open(`/analyze/export-task/${taskId}/download`);
}

// 完整使用示例
async function exportOeeData(workshopId, startTime, endTime, exportType) {
  try {
    // 创建任务
    const taskId = await createExportTask(workshopId, startTime, endTime, exportType);
    
    // 开始轮询状态
    pollTaskStatus(taskId);
    
  } catch (error) {
    console.error('导出失败:', error);
  }
}
```

## 系统配置

### 文件存储

- 导出文件存储在 `exports/` 目录下
- 文件保留7天后自动清理
- 任务记录保留30天后自动清理

### 线程池配置

异步任务使用专用的OEE计算线程池：
- 核心线程数: CPU核心数
- 最大线程数: CPU核心数 × 2
- 队列容量: 100

### 定时清理

系统每天凌晨2点自动清理过期文件和任务记录。

## 注意事项

1. **权限控制**: 目前所有任务都使用"system"用户创建，后续可根据需要添加用户权限验证
2. **并发限制**: 线程池有容量限制，避免同时提交过多导出任务
3. **文件大小**: 大文件导出可能需要较长时间，建议合理设置轮询间隔
4. **错误处理**: 任务失败时会记录详细错误信息，便于问题排查

## 监控和维护

- 查看日志文件了解任务执行情况
- 监控 `exports/` 目录的磁盘使用情况
- 定期检查任务执行性能和成功率
