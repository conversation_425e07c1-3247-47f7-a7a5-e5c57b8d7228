package com.github.cret.web.oee.task;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.service.AnalyzeService;
import com.github.cret.web.oee.service.FileStorageService;
import com.github.cret.web.oee.service.TaskService;
import com.github.cret.web.oee.service.WorkshopService;

/**
 * OEE导出异步任务处理类
 */
@Service
public class OeeExportTask {

	private static final Logger log = LoggerFactory.getLogger(OeeExportTask.class);

	private final AnalyzeService analyzeService;

	private final TaskService taskService;

	private final WorkshopService workshopService;

	private final FileStorageService fileStorageService;

	public OeeExportTask(AnalyzeService analyzeService, TaskService taskService, WorkshopService workshopService,
			FileStorageService fileStorageService) {
		this.analyzeService = analyzeService;
		this.taskService = taskService;
		this.workshopService = workshopService;
		this.fileStorageService = fileStorageService;
	}

	/**
	 * 异步执行OEE月度导出任务
	 * @param taskId 任务ID
	 * @param taskParams 任务参数（JSON格式）
	 */
	@Async("oeeCalculationExecutor")
	public void executeOeeMonthlyExport(String taskId, String workshopId, AnalyzeQuery query) {
		log.info("Starting OEE monthly export task: taskId={}", taskId);

		try {
			// 标记任务为处理中
			taskService.markTaskAsProcessing(taskId);

			// 更新进度：开始数据查询
			taskService.updateTaskProgress(taskId, 20);

			// 执行数据查询
			List<OeeResult> oeeResults = analyzeService.getWorkshopMonthlyOee(workshopId, query);

			// 更新进度：数据查询完成
			taskService.updateTaskProgress(taskId, 60);

			// 生成文件名
			String fileName = generateFileName(workshopId, query);
			String filePath = generateFilePath(fileName);

			// 更新进度：开始生成Excel文件
			taskService.updateTaskProgress(taskId, 80);

			// 生成Excel文件
			generateExcelFile(oeeResults, filePath);

			// 更新进度：文件生成完成
			taskService.updateTaskProgress(taskId, 100);

			// 标记任务为完成
			taskService.markTaskAsCompleted(taskId, filePath, fileName);

			log.info("OEE monthly export task completed successfully: taskId={}, fileName={}", taskId, fileName);

		}
		catch (Exception e) {
			log.error("OEE monthly export task failed: taskId={}", taskId, e);
			taskService.markTaskAsFailed(taskId, e.getMessage());
		}
	}

	/**
	 * 异步执行OEE日度导出任务
	 * @param taskId 任务ID
	 * @param taskParams 任务参数（JSON格式）
	 */
	@Async("oeeCalculationExecutor")
	public void executeOeeDailyExport(String taskId, String workshopId, AnalyzeQuery query) {
		log.info("Starting OEE daily export task: taskId={}", taskId);

		try {
			// 标记任务为处理中
			taskService.markTaskAsProcessing(taskId);

			// 更新进度：开始数据查询
			taskService.updateTaskProgress(taskId, 20);

			// 执行数据查询
			List<OeeResult> oeeResults = analyzeService.getWorkshopDailyOee(workshopId, query);

			// 更新进度：数据查询完成
			taskService.updateTaskProgress(taskId, 60);

			// 生成文件名
			String fileName = generateDailyFileName(workshopId, query);
			String filePath = generateFilePath(fileName);

			// 更新进度：开始生成Excel文件
			taskService.updateTaskProgress(taskId, 80);

			// 生成Excel文件
			generateExcelFile(oeeResults, filePath);

			// 更新进度：文件生成完成
			taskService.updateTaskProgress(taskId, 100);

			// 标记任务为完成
			taskService.markTaskAsCompleted(taskId, filePath, fileName);

			log.info("OEE daily export task completed successfully: taskId={}, fileName={}", taskId, fileName);

		}
		catch (Exception e) {
			log.error("OEE daily export task failed: taskId={}", taskId, e);
			taskService.markTaskAsFailed(taskId, e.getMessage());
		}
	}

	/**
	 * 生成月度导出文件名
	 */
	private String generateFileName(String workshopId, AnalyzeQuery query) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String monthStr = sdf.format(query.getStartTime());
		return String.format("%s车间%s月度OEE数据_%s.xlsx", workshopId, monthStr, System.currentTimeMillis());
	}

	/**
	 * 生成日度导出文件名
	 */
	private String generateDailyFileName(String workshopId, AnalyzeQuery query) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateStr = sdf.format(query.getStartTime());
		return String.format("%s车间%s日度OEE数据_%s.xlsx", workshopId, dateStr, System.currentTimeMillis());
	}

	/**
	 * 生成文件完整路径
	 */
	private String generateFilePath(String fileName) {
		return Paths.get(fileStorageService.getExportDirectory(), fileName).toString();
	}

	/**
	 * 生成Excel文件
	 */
	private void generateExcelFile(List<OeeResult> oeeResults, String filePath) throws IOException {
		// 确保导出目录存在
		fileStorageService.ensureExportDirectoryExists();

		File file = new File(filePath);
		try (FileOutputStream outputStream = new FileOutputStream(file)) {
			EasyExcel.write(outputStream, OeeResult.class).sheet("OEE指标").doWrite(oeeResults);
		}
		log.info("Excel file generated successfully: {}", filePath);
	}

}
